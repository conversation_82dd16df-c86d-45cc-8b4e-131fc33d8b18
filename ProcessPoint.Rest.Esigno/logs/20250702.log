2025-07-02 08:37:56.920 +02:00 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2025-07-02 08:37:57.065 +02:00 [DBG] Hosting starting
2025-07-02 08:37:57.093 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-07-02 08:37:57.096 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-62613a68-acd0-4198-a2dc-fd288203331f.xml'.
2025-07-02 08:37:57.101 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-f557cdd1-6cf5-426e-918b-a1a451e50f7c.xml'.
2025-07-02 08:37:57.104 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-7c39ab3b-e83c-4526-97ba-341254a092a6.xml'.
2025-07-02 08:37:57.107 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-aa556005-fc1c-45cb-b2a8-5da5b47d6031.xml'.
2025-07-02 08:37:57.108 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-573b67e1-3f60-4edc-812d-6d10ba8f545e.xml'.
2025-07-02 08:37:57.109 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-bdab4f65-7d40-4823-9ab5-32f587b06b51.xml'.
2025-07-02 08:37:57.111 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-2ac45e63-6d4c-4195-af6c-7d189f29e805.xml'.
2025-07-02 08:37:57.112 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-e33865f6-1366-4409-aeff-4b5d9c08fd6b.xml'.
2025-07-02 08:37:57.115 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-110ed128-38a9-4672-b9f4-0db76829c6ba.xml'.
2025-07-02 08:37:57.125 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-1551515c-b6d2-48e5-9f3e-9b041ceb8348.xml'.
2025-07-02 08:37:57.127 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-ccc7f0bf-3b7f-4f36-94c1-a944e0c08ceb.xml'.
2025-07-02 08:37:57.129 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-01a1f70d-478b-4435-a13b-e020f77dd56d.xml'.
2025-07-02 08:37:57.129 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-04c3120c-d0c7-47cb-b9c8-d3b790a82cc6.xml'.
2025-07-02 08:37:57.132 +02:00 [DBG] Found key {62613a68-acd0-4198-a2dc-fd288203331f}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {f557cdd1-6cf5-426e-918b-a1a451e50f7c}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {7c39ab3b-e83c-4526-97ba-341254a092a6}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {aa556005-fc1c-45cb-b2a8-5da5b47d6031}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {573b67e1-3f60-4edc-812d-6d10ba8f545e}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {bdab4f65-7d40-4823-9ab5-32f587b06b51}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {2ac45e63-6d4c-4195-af6c-7d189f29e805}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {e33865f6-1366-4409-aeff-4b5d9c08fd6b}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {110ed128-38a9-4672-b9f4-0db76829c6ba}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {1551515c-b6d2-48e5-9f3e-9b041ceb8348}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {ccc7f0bf-3b7f-4f36-94c1-a944e0c08ceb}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {01a1f70d-478b-4435-a13b-e020f77dd56d}.
2025-07-02 08:37:57.134 +02:00 [DBG] Found key {04c3120c-d0c7-47cb-b9c8-d3b790a82cc6}.
2025-07-02 08:37:57.138 +02:00 [DBG] Considering key {2ac45e63-6d4c-4195-af6c-7d189f29e805} with expiration date 2025-08-12 09:00:30Z as default key.
2025-07-02 08:37:57.139 +02:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-02 08:37:57.161 +02:00 [DBG] Using managed symmetric algorithm 'System.Security.Cryptography.Aes'.
2025-07-02 08:37:57.162 +02:00 [DBG] Using managed keyed hash algorithm 'System.Security.Cryptography.HMACSHA256'.
2025-07-02 08:37:57.266 +02:00 [DBG] Using key {2ac45e63-6d4c-4195-af6c-7d189f29e805} as the default key.
2025-07-02 08:37:57.267 +02:00 [DBG] Key ring with default key {2ac45e63-6d4c-4195-af6c-7d189f29e805} was loaded during application startup.
2025-07-02 08:37:57.869 +02:00 [INF] Now listening on: https://localhost:44301
2025-07-02 08:37:57.897 +02:00 [INF] Now listening on: http://localhost:5063
2025-07-02 08:37:57.897 +02:00 [DBG] Loaded hosting startup assembly ProcessPoint.Rest.Esigno
2025-07-02 08:37:57.897 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 08:37:57.897 +02:00 [INF] Hosting environment: Development
2025-07-02 08:37:57.897 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno
2025-07-02 08:37:57.897 +02:00 [DBG] Hosting started
2025-07-02 08:37:58.047 +02:00 [DBG] Connection id "0HNDP6BTSP70L" accepted.
2025-07-02 08:37:58.049 +02:00 [DBG] Connection id "0HNDP6BTSP70L" started.
2025-07-02 08:37:58.144 +02:00 [DBG] Connection 0HNDP6BTSP70L established using the following protocol: "Tls12"
2025-07-02 08:37:58.188 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "SETTINGS" frame for stream ID 0 with length 24 and flags "NONE".
2025-07-02 08:37:58.194 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 08:37:58.218 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "SETTINGS" frame for stream ID 0 with length 18 and flags "NONE".
2025-07-02 08:37:58.222 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 08:37:58.223 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 08:37:58.223 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "HEADERS" frame for stream ID 1 with length 306 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.238 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 08:37:58.274 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/index.html - null null
2025-07-02 08:37:58.277 +02:00 [DBG] Wildcard detected, all requests with hosts will be allowed.
2025-07-02 08:37:58.277 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.403 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "HEADERS" frame for stream ID 1 with length 66 and flags "END_HEADERS".
2025-07-02 08:37:58.404 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 1 with length 4761 and flags "NONE".
2025-07-02 08:37:58.411 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 1 with length 0 and flags "END_STREAM".
2025-07-02 08:37:58.413 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/index.html - 200 null text/html;charset=utf-8 140.7264ms
2025-07-02 08:37:58.422 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "HEADERS" frame for stream ID 3 with length 98 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.423 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "HEADERS" frame for stream ID 5 with length 48 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.424 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/swagger-ui-standalone-preset.js - null null
2025-07-02 08:37:58.424 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.424 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/swagger-ui-bundle.js - null null
2025-07-02 08:37:58.425 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.429 +02:00 [VRB] Range header's value is empty.
2025-07-02 08:37:58.429 +02:00 [VRB] Range header's value is empty.
2025-07-02 08:37:58.431 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "HEADERS" frame for stream ID 5 with length 86 and flags "END_HEADERS".
2025-07-02 08:37:58.432 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.432 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.432 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.432 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.433 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "HEADERS" frame for stream ID 3 with length 35 and flags "END_HEADERS".
2025-07-02 08:37:58.433 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.433 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.433 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.433 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.493 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.494 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.494 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.494 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.496 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.496 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.496 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.496 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.499 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.499 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.499 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.499 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.500 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.501 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 867 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.502 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.503 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.503 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.503 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.503 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.504 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.505 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.506 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.506 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.506 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.506 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.507 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "WINDOW_UPDATE" frame for stream ID 3 with length 4 and flags 0x0.
2025-07-02 08:37:58.533 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.534 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.534 +02:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-02 08:37:58.534 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.534 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.534 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 110.0631ms
2025-07-02 08:37:58.535 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 5 with length 0 and flags "END_STREAM".
2025-07-02 08:37:58.535 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.535 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.535 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.535 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.536 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 16384 and flags "NONE".
2025-07-02 08:37:58.537 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 12960 and flags "NONE".
2025-07-02 08:37:58.537 +02:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-02 08:37:58.537 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 3 with length 0 and flags "END_STREAM".
2025-07-02 08:37:58.537 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 112.5987ms
2025-07-02 08:37:58.647 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "HEADERS" frame for stream ID 7 with length 59 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:37:58.670 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/v1/swagger.json - null null
2025-07-02 08:37:58.670 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.670 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "HEADERS" frame for stream ID 9 with length 39 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:37:58.670 +02:00 [VRB] Connection id "0HNDP6BTSP70L" received "HEADERS" frame for stream ID 11 with length 33 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:37:58.741 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/favicon-32x32.png - null null
2025-07-02 08:37:58.767 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/favicon-16x16.png - null null
2025-07-02 08:37:58.767 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.767 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.767 +02:00 [VRB] Range header's value is empty.
2025-07-02 08:37:58.767 +02:00 [VRB] Range header's value is empty.
2025-07-02 08:37:58.788 +02:00 [INF] Sending file. Request path: '/favicon-16x16.png'. Physical path: 'N/A'
2025-07-02 08:37:58.788 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "HEADERS" frame for stream ID 11 with length 41 and flags "END_HEADERS".
2025-07-02 08:37:58.788 +02:00 [DBG] Connection id "0HNDP6BTSP70M" accepted.
2025-07-02 08:37:58.788 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 11 with length 665 and flags "NONE".
2025-07-02 08:37:58.788 +02:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-07-02 08:37:58.788 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/favicon-16x16.png - 200 665 image/png 47.2432ms
2025-07-02 08:37:58.789 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/favicon-32x32.png - 200 628 image/png 94.4043ms
2025-07-02 08:37:58.789 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "HEADERS" frame for stream ID 9 with length 31 and flags "END_HEADERS".
2025-07-02 08:37:58.789 +02:00 [DBG] Connection id "0HNDP6BTSP70M" started.
2025-07-02 08:37:58.789 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 9 with length 628 and flags "END_STREAM".
2025-07-02 08:37:58.789 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 11 with length 0 and flags "END_STREAM".
2025-07-02 08:37:58.817 +02:00 [DBG] Connection 0HNDP6BTSP70M established using the following protocol: "Tls12"
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "SETTINGS" frame for stream ID 0 with length 24 and flags "NONE".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "SETTINGS" frame for stream ID 0 with length 18 and flags "NONE".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "HEADERS" frame for stream ID 1 with length 137 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "HEADERS" frame for stream ID 3 with length 29 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "HEADERS" frame for stream ID 5 with length 23 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "HEADERS" frame for stream ID 7 with length 33 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.822 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "HEADERS" frame for stream ID 9 with length 33 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:37:58.822 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/apple-touch-icon.png - null null
2025-07-02 08:37:58.822 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/favicon.ico - null null
2025-07-02 08:37:58.822 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/apple-touch-icon-precomposed.png - null null
2025-07-02 08:37:58.823 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.823 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.823 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/favicon-32x32.png - null null
2025-07-02 08:37:58.846 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 08:37:58.846 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.823 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.823 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/favicon-16x16.png - null null
2025-07-02 08:37:58.846 +02:00 [VRB] Range header's value is empty.
2025-07-02 08:37:58.868 +02:00 [DBG] The request path  does not match the path filter
2025-07-02 08:37:58.868 +02:00 [DBG] The request path  does not match the path filter
2025-07-02 08:37:58.868 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:37:58.868 +02:00 [DBG] The request path  does not match the path filter
2025-07-02 08:37:58.868 +02:00 [VRB] Range header's value is empty.
2025-07-02 08:37:58.868 +02:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-07-02 08:37:58.869 +02:00 [INF] Sending file. Request path: '/favicon-16x16.png'. Physical path: 'N/A'
2025-07-02 08:37:58.869 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/favicon-16x16.png - 200 665 image/png 46.1605ms
2025-07-02 08:37:58.869 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "HEADERS" frame for stream ID 9 with length 115 and flags "END_HEADERS".
2025-07-02 08:37:58.869 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/favicon-32x32.png - 200 628 image/png 46.195ms
2025-07-02 08:37:58.869 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "DATA" frame for stream ID 9 with length 628 and flags "END_STREAM".
2025-07-02 08:37:58.895 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "HEADERS" frame for stream ID 7 with length 31 and flags "END_HEADERS".
2025-07-02 08:37:58.895 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "DATA" frame for stream ID 7 with length 665 and flags "END_STREAM".
2025-07-02 08:37:58.911 +02:00 [DBG] No candidates found for the request path '/apple-touch-icon.png'
2025-07-02 08:37:58.911 +02:00 [DBG] No candidates found for the request path '/favicon.ico'
2025-07-02 08:37:58.911 +02:00 [DBG] No candidates found for the request path '/apple-touch-icon-precomposed.png'
2025-07-02 08:37:58.911 +02:00 [DBG] Request did not match any endpoints
2025-07-02 08:37:58.911 +02:00 [DBG] Request did not match any endpoints
2025-07-02 08:37:58.912 +02:00 [DBG] Request did not match any endpoints
2025-07-02 08:37:58.994 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-07-02 08:37:58.994 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-07-02 08:37:58.994 +02:00 [WRN] Authentication failed: The format of value '<null>' is invalid.
2025-07-02 08:37:58.995 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-07-02 08:37:58.995 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-07-02 08:37:58.995 +02:00 [INF] BasicAuthentication was not authenticated. Failure message: Authentication failed: The format of value '<null>' is invalid.
2025-07-02 08:37:58.996 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/apple-touch-icon-precomposed.png - 404 0 null 173.7347ms
2025-07-02 08:37:58.996 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "HEADERS" frame for stream ID 3 with length 7 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:37:58.996 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/apple-touch-icon.png - 404 0 null 173.7358ms
2025-07-02 08:37:58.996 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/favicon.ico - 404 0 null 173.7297ms
2025-07-02 08:37:58.996 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "HEADERS" frame for stream ID 5 with length 7 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:37:58.996 +02:00 [VRB] Connection id "0HNDP6BTSP70M" sending "HEADERS" frame for stream ID 1 with length 7 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:37:58.996 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:44301/apple-touch-icon-precomposed.png, Response status code: 404
2025-07-02 08:37:58.996 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:44301/apple-touch-icon.png, Response status code: 404
2025-07-02 08:37:58.996 +02:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:44301/favicon.ico, Response status code: 404
2025-07-02 08:37:59.036 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "HEADERS" frame for stream ID 7 with length 35 and flags "END_HEADERS".
2025-07-02 08:37:59.036 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 7 with length 8711 and flags "NONE".
2025-07-02 08:37:59.039 +02:00 [VRB] Connection id "0HNDP6BTSP70L" sending "DATA" frame for stream ID 7 with length 0 and flags "END_STREAM".
2025-07-02 08:37:59.039 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 391.1612ms
2025-07-02 08:38:40.269 +02:00 [DBG] Connection id "0HNDP6BTSP70N" received FIN.
2025-07-02 08:38:40.270 +02:00 [DBG] Connection id "0HNDP6BTSP70N" accepted.
2025-07-02 08:38:40.271 +02:00 [DBG] Connection id "0HNDP6BTSP70O" accepted.
2025-07-02 08:38:40.271 +02:00 [DBG] Connection id "0HNDP6BTSP70N" started.
2025-07-02 08:38:40.271 +02:00 [DBG] Connection id "0HNDP6BTSP70O" started.
2025-07-02 08:38:40.275 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 08:38:40.294 +02:00 [DBG] Connection id "0HNDP6BTSP70N" stopped.
2025-07-02 08:38:40.294 +02:00 [DBG] Connection id "0HNDP6BTSP70N" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:38:40.348 +02:00 [DBG] Connection 0HNDP6BTSP70O established using the following protocol: "Tls12"
2025-07-02 08:38:40.355 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 96979
2025-07-02 08:38:40.355 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:38:40.355 +02:00 [DBG] POST requests are not supported
2025-07-02 08:38:40.360 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 08:38:40.362 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 08:38:40.362 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:38:40.363 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 08:38:40.365 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 08:38:40.368 +02:00 [DBG] Authorization was successful.
2025-07-02 08:38:40.368 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:38:40.385 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 08:38:40.386 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 08:38:40.386 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 08:38:40.386 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 08:38:40.386 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 08:38:40.386 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 08:38:40.388 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:38:40.503 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:38:40.515 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:38:40.516 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 08:38:40.516 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 08:38:40.532 +02:00 [DBG] Connection id "0HNDP6BTSP70O", Request id "0HNDP6BTSP70O:00000001": started reading request body.
2025-07-02 08:38:40.541 +02:00 [DBG] Connection id "0HNDP6BTSP70O", Request id "0HNDP6BTSP70O:00000001": done reading request body.
2025-07-02 08:38:40.547 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 08:38:40.547 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:38:40.547 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:38:40.547 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:38:40.564 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:38:40.566 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:38:40.567 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:38:40.567 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:38:40.567 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:38:40.572 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 08:38:40.572 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 08:39:13.028 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:39:13.028 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:39:13.028 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:39:13.028 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:39:13.031 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 32642.3052ms
2025-07-02 08:39:13.032 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:39:13.032 +02:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.Single[TSource](IEnumerable`1 source, Func`2 predicate)
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTasks(List`1 tokens, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 164
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTaskGroup(Document document, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 124
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionTasks(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 71
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 34
   at ProcessPoint.Rest.Esigno.Esigno.EsignoService.SetConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Esigno/EsignoService.cs:line 86
   at ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession(Envelope rq) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Controllers/EsignoController.cs:line 37
   at lambda_method5(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-02 08:39:13.037 +02:00 [DBG] Connection id "0HNDP6BTSP70O" completed keep alive response.
2025-07-02 08:39:13.037 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 500 null text/plain; charset=utf-8 32682.5159ms
2025-07-02 08:39:59.806 +02:00 [VRB] Connection id "0HNDP6BTSP70M" received "GOAWAY" frame for stream ID 0 with length 8 and flags 0x0.
2025-07-02 08:39:59.807 +02:00 [DBG] Connection id "0HNDP6BTSP70M" received FIN.
2025-07-02 08:39:59.819 +02:00 [DBG] The connection queue processing loop for 0HNDP6BTSP70M completed.
2025-07-02 08:39:59.821 +02:00 [DBG] Connection id "0HNDP6BTSP70M" is closed. The last processed stream ID was 9.
2025-07-02 08:39:59.822 +02:00 [DBG] Connection id "0HNDP6BTSP70M" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:39:59.823 +02:00 [DBG] Connection id "0HNDP6BTSP70M" stopped.
2025-07-02 08:40:48.837 +02:00 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2025-07-02 08:40:48.936 +02:00 [DBG] Hosting starting
2025-07-02 08:40:48.954 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-07-02 08:40:48.958 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-62613a68-acd0-4198-a2dc-fd288203331f.xml'.
2025-07-02 08:40:48.961 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-f557cdd1-6cf5-426e-918b-a1a451e50f7c.xml'.
2025-07-02 08:40:48.961 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-7c39ab3b-e83c-4526-97ba-341254a092a6.xml'.
2025-07-02 08:40:48.962 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-aa556005-fc1c-45cb-b2a8-5da5b47d6031.xml'.
2025-07-02 08:40:48.962 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-573b67e1-3f60-4edc-812d-6d10ba8f545e.xml'.
2025-07-02 08:40:48.963 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-bdab4f65-7d40-4823-9ab5-32f587b06b51.xml'.
2025-07-02 08:40:48.963 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-2ac45e63-6d4c-4195-af6c-7d189f29e805.xml'.
2025-07-02 08:40:48.963 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-e33865f6-1366-4409-aeff-4b5d9c08fd6b.xml'.
2025-07-02 08:40:48.964 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-110ed128-38a9-4672-b9f4-0db76829c6ba.xml'.
2025-07-02 08:40:48.964 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-1551515c-b6d2-48e5-9f3e-9b041ceb8348.xml'.
2025-07-02 08:40:48.964 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-ccc7f0bf-3b7f-4f36-94c1-a944e0c08ceb.xml'.
2025-07-02 08:40:48.964 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-01a1f70d-478b-4435-a13b-e020f77dd56d.xml'.
2025-07-02 08:40:48.965 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-04c3120c-d0c7-47cb-b9c8-d3b790a82cc6.xml'.
2025-07-02 08:40:48.968 +02:00 [DBG] Found key {62613a68-acd0-4198-a2dc-fd288203331f}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {f557cdd1-6cf5-426e-918b-a1a451e50f7c}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {7c39ab3b-e83c-4526-97ba-341254a092a6}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {aa556005-fc1c-45cb-b2a8-5da5b47d6031}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {573b67e1-3f60-4edc-812d-6d10ba8f545e}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {bdab4f65-7d40-4823-9ab5-32f587b06b51}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {2ac45e63-6d4c-4195-af6c-7d189f29e805}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {e33865f6-1366-4409-aeff-4b5d9c08fd6b}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {110ed128-38a9-4672-b9f4-0db76829c6ba}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {1551515c-b6d2-48e5-9f3e-9b041ceb8348}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {ccc7f0bf-3b7f-4f36-94c1-a944e0c08ceb}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {01a1f70d-478b-4435-a13b-e020f77dd56d}.
2025-07-02 08:40:48.969 +02:00 [DBG] Found key {04c3120c-d0c7-47cb-b9c8-d3b790a82cc6}.
2025-07-02 08:40:48.973 +02:00 [DBG] Considering key {2ac45e63-6d4c-4195-af6c-7d189f29e805} with expiration date 2025-08-12 09:00:30Z as default key.
2025-07-02 08:40:48.974 +02:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-02 08:40:48.984 +02:00 [DBG] Using managed symmetric algorithm 'System.Security.Cryptography.Aes'.
2025-07-02 08:40:48.984 +02:00 [DBG] Using managed keyed hash algorithm 'System.Security.Cryptography.HMACSHA256'.
2025-07-02 08:40:48.987 +02:00 [DBG] Using key {2ac45e63-6d4c-4195-af6c-7d189f29e805} as the default key.
2025-07-02 08:40:48.987 +02:00 [DBG] Key ring with default key {2ac45e63-6d4c-4195-af6c-7d189f29e805} was loaded during application startup.
2025-07-02 08:40:49.518 +02:00 [INF] Now listening on: https://localhost:44301
2025-07-02 08:40:49.518 +02:00 [INF] Now listening on: http://localhost:5063
2025-07-02 08:40:49.518 +02:00 [DBG] Loaded hosting startup assembly ProcessPoint.Rest.Esigno
2025-07-02 08:40:49.518 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 08:40:49.518 +02:00 [INF] Hosting environment: Development
2025-07-02 08:40:49.518 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno
2025-07-02 08:40:49.518 +02:00 [DBG] Hosting started
2025-07-02 08:40:49.948 +02:00 [DBG] Connection id "0HNDP6DH456I9" received FIN.
2025-07-02 08:40:49.951 +02:00 [DBG] Connection id "0HNDP6DH456I9" accepted.
2025-07-02 08:40:49.952 +02:00 [DBG] Connection id "0HNDP6DH456I9" started.
2025-07-02 08:40:49.959 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 08:40:49.982 +02:00 [DBG] Connection id "0HNDP6DH456I9" stopped.
2025-07-02 08:40:49.985 +02:00 [DBG] Connection id "0HNDP6DH456I9" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:40:50.044 +02:00 [DBG] Connection id "0HNDP6DH456IA" accepted.
2025-07-02 08:40:50.046 +02:00 [DBG] Connection id "0HNDP6DH456IA" started.
2025-07-02 08:40:50.091 +02:00 [DBG] Connection 0HNDP6DH456IA established using the following protocol: "Tls12"
2025-07-02 08:40:50.110 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "SETTINGS" frame for stream ID 0 with length 24 and flags "NONE".
2025-07-02 08:40:50.112 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 08:40:50.113 +02:00 [VRB] Connection id "0HNDP6DH456IA" received "SETTINGS" frame for stream ID 0 with length 18 and flags "NONE".
2025-07-02 08:40:50.113 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 08:40:50.113 +02:00 [VRB] Connection id "0HNDP6DH456IA" received "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 08:40:50.114 +02:00 [VRB] Connection id "0HNDP6DH456IA" received "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 08:40:50.138 +02:00 [VRB] Connection id "0HNDP6DH456IA" received "HEADERS" frame for stream ID 1 with length 306 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 08:40:50.158 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/index.html - null null
2025-07-02 08:40:50.158 +02:00 [DBG] Wildcard detected, all requests with hosts will be allowed.
2025-07-02 08:40:50.158 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:40:50.250 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "HEADERS" frame for stream ID 1 with length 66 and flags "END_HEADERS".
2025-07-02 08:40:50.251 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "DATA" frame for stream ID 1 with length 4761 and flags "NONE".
2025-07-02 08:40:50.255 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "DATA" frame for stream ID 1 with length 0 and flags "END_STREAM".
2025-07-02 08:40:50.256 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/index.html - 200 null text/html;charset=utf-8 98.7477ms
2025-07-02 08:40:50.389 +02:00 [VRB] Connection id "0HNDP6DH456IA" received "HEADERS" frame for stream ID 3 with length 100 and flags "END_STREAM, END_HEADERS".
2025-07-02 08:40:50.412 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/v1/swagger.json - null null
2025-07-02 08:40:50.412 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:40:50.490 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "HEADERS" frame for stream ID 3 with length 35 and flags "END_HEADERS".
2025-07-02 08:40:50.491 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "DATA" frame for stream ID 3 with length 8719 and flags "NONE".
2025-07-02 08:40:50.546 +02:00 [VRB] Connection id "0HNDP6DH456IA" sending "DATA" frame for stream ID 3 with length 0 and flags "END_STREAM".
2025-07-02 08:40:50.546 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 134.3656ms
2025-07-02 08:42:03.825 +02:00 [DBG] Connection id "0HNDP6DH456IB" received FIN.
2025-07-02 08:42:03.855 +02:00 [DBG] Connection id "0HNDP6DH456IB" accepted.
2025-07-02 08:42:03.857 +02:00 [DBG] Connection id "0HNDP6DH456IB" started.
2025-07-02 08:42:03.859 +02:00 [DBG] Connection id "0HNDP6DH456IC" accepted.
2025-07-02 08:42:03.859 +02:00 [DBG] Connection id "0HNDP6DH456IC" started.
2025-07-02 08:42:03.859 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 08:42:03.860 +02:00 [DBG] Connection id "0HNDP6DH456IB" stopped.
2025-07-02 08:42:03.860 +02:00 [DBG] Connection id "0HNDP6DH456IB" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:42:03.889 +02:00 [DBG] Connection 0HNDP6DH456IC established using the following protocol: "Tls12"
2025-07-02 08:42:03.895 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 96979
2025-07-02 08:42:03.895 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:42:03.897 +02:00 [DBG] POST requests are not supported
2025-07-02 08:42:03.916 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 08:42:03.917 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 08:42:03.918 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:42:03.918 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 08:42:03.924 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 08:42:03.928 +02:00 [DBG] Authorization was successful.
2025-07-02 08:42:03.929 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:42:03.945 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 08:42:03.946 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 08:42:03.946 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 08:42:03.946 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 08:42:03.946 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 08:42:03.946 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 08:42:03.948 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:42:04.036 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:42:04.044 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:42:04.045 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 08:42:04.045 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 08:42:04.060 +02:00 [DBG] Connection id "0HNDP6DH456IC", Request id "0HNDP6DH456IC:00000001": started reading request body.
2025-07-02 08:42:04.067 +02:00 [DBG] Connection id "0HNDP6DH456IC", Request id "0HNDP6DH456IC:00000001": done reading request body.
2025-07-02 08:42:04.072 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 08:42:04.072 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:42:04.072 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:42:04.072 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:42:04.088 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:42:04.089 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:42:04.089 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:42:04.089 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:42:04.089 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:42:04.094 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 08:42:04.094 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 08:43:50.359 +02:00 [DBG] Connection id "0HNDP6DH456IA" received FIN.
2025-07-02 08:43:50.362 +02:00 [VRB] Connection id "0HNDP6DH456IA" received "GOAWAY" frame for stream ID 0 with length 8 and flags 0x0.
2025-07-02 08:43:50.367 +02:00 [DBG] The connection queue processing loop for 0HNDP6DH456IA completed.
2025-07-02 08:43:50.368 +02:00 [DBG] Connection id "0HNDP6DH456IA" is closed. The last processed stream ID was 3.
2025-07-02 08:43:50.369 +02:00 [DBG] Connection id "0HNDP6DH456IA" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:43:50.370 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:43:50.370 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:43:50.370 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:43:50.370 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:43:50.371 +02:00 [DBG] Connection id "0HNDP6DH456IA" stopped.
2025-07-02 08:43:50.373 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 106424.8272ms
2025-07-02 08:43:50.374 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:43:50.374 +02:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.Single[TSource](IEnumerable`1 source, Func`2 predicate)
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTasks(List`1 tokens, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 164
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTaskGroup(Document document, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 124
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionTasks(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 71
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 34
   at ProcessPoint.Rest.Esigno.Esigno.EsignoService.SetConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Esigno/EsignoService.cs:line 86
   at ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession(Envelope rq) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Controllers/EsignoController.cs:line 37
   at lambda_method5(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-02 08:43:50.378 +02:00 [DBG] Connection id "0HNDP6DH456IC" completed keep alive response.
2025-07-02 08:43:50.378 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 500 null text/plain; charset=utf-8 106484.1489ms
2025-07-02 08:44:00.119 +02:00 [DBG] Connection id "0HNDP6DH456ID" received FIN.
2025-07-02 08:44:00.121 +02:00 [DBG] Connection id "0HNDP6DH456ID" accepted.
2025-07-02 08:44:00.121 +02:00 [DBG] Connection id "0HNDP6DH456ID" started.
2025-07-02 08:44:00.121 +02:00 [DBG] Connection id "0HNDP6DH456IE" accepted.
2025-07-02 08:44:00.122 +02:00 [DBG] Connection id "0HNDP6DH456IE" started.
2025-07-02 08:44:00.122 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 08:44:00.122 +02:00 [DBG] Connection id "0HNDP6DH456ID" stopped.
2025-07-02 08:44:00.123 +02:00 [DBG] Connection id "0HNDP6DH456ID" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:44:00.147 +02:00 [DBG] Connection 0HNDP6DH456IE established using the following protocol: "Tls12"
2025-07-02 08:44:00.174 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 96977
2025-07-02 08:44:00.174 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:44:00.174 +02:00 [DBG] POST requests are not supported
2025-07-02 08:44:00.175 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 08:44:00.175 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 08:44:00.175 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:44:00.175 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 08:44:00.176 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 08:44:00.177 +02:00 [DBG] Authorization was successful.
2025-07-02 08:44:00.177 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:44:00.180 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 08:44:00.180 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 08:44:00.180 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 08:44:00.180 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 08:44:00.180 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 08:44:00.180 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 08:44:00.180 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:44:00.185 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:44:00.185 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:44:00.186 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 08:44:00.186 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 08:44:00.186 +02:00 [DBG] Connection id "0HNDP6DH456IE", Request id "0HNDP6DH456IE:00000001": started reading request body.
2025-07-02 08:44:00.186 +02:00 [DBG] Connection id "0HNDP6DH456IE", Request id "0HNDP6DH456IE:00000001": done reading request body.
2025-07-02 08:44:00.187 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 08:44:00.187 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:44:00.187 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:44:00.187 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:44:00.187 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:44:00.187 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:44:00.187 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:44:00.187 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:44:00.187 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:44:00.187 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 08:44:00.187 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 08:44:10.721 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:44:10.721 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:44:10.721 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:44:10.721 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:44:10.721 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 10541.3479ms
2025-07-02 08:44:10.721 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:44:10.722 +02:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.Single[TSource](IEnumerable`1 source, Func`2 predicate)
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTasks(List`1 tokens, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 164
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTaskGroup(Document document, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 124
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionTasks(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 71
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 34
   at ProcessPoint.Rest.Esigno.Esigno.EsignoService.SetConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Esigno/EsignoService.cs:line 86
   at ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession(Envelope rq) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Controllers/EsignoController.cs:line 37
   at lambda_method5(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-02 08:44:10.723 +02:00 [DBG] Connection id "0HNDP6DH456IE" completed keep alive response.
2025-07-02 08:44:10.723 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 500 null text/plain; charset=utf-8 10549.7457ms
2025-07-02 08:49:51.193 +02:00 [DBG] Connection id "0HNDP6DH456IF" received FIN.
2025-07-02 08:49:51.197 +02:00 [DBG] Connection id "0HNDP6DH456IF" accepted.
2025-07-02 08:49:51.197 +02:00 [DBG] Connection id "0HNDP6DH456IF" started.
2025-07-02 08:49:51.223 +02:00 [DBG] Connection id "0HNDP6DH456IG" accepted.
2025-07-02 08:49:51.224 +02:00 [DBG] Connection id "0HNDP6DH456IG" started.
2025-07-02 08:49:51.224 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 08:49:51.225 +02:00 [DBG] Connection id "0HNDP6DH456IF" stopped.
2025-07-02 08:49:51.227 +02:00 [DBG] Connection id "0HNDP6DH456IF" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:49:51.251 +02:00 [DBG] Connection 0HNDP6DH456IG established using the following protocol: "Tls12"
2025-07-02 08:49:51.305 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 96962
2025-07-02 08:49:51.305 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:49:51.306 +02:00 [DBG] POST requests are not supported
2025-07-02 08:49:51.306 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 08:49:51.306 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 08:49:51.306 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:49:51.306 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 08:49:51.308 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 08:49:51.309 +02:00 [DBG] Authorization was successful.
2025-07-02 08:49:51.309 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:49:51.309 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 08:49:51.309 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 08:49:51.309 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 08:49:51.309 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 08:49:51.309 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 08:49:51.309 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 08:49:51.309 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:49:51.312 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:49:51.312 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:49:51.312 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 08:49:51.312 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 08:49:51.312 +02:00 [DBG] Connection id "0HNDP6DH456IG", Request id "0HNDP6DH456IG:00000001": started reading request body.
2025-07-02 08:49:51.312 +02:00 [DBG] Connection id "0HNDP6DH456IG", Request id "0HNDP6DH456IG:00000001": done reading request body.
2025-07-02 08:49:51.313 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 08:49:51.313 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:49:51.313 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:49:51.313 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:49:51.313 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:49:51.313 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:49:51.313 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:49:51.313 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:49:51.313 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:49:51.313 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 08:49:51.313 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 08:50:00.521 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:50:00.521 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:50:00.521 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:50:00.521 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:50:00.521 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 9212.499ms
2025-07-02 08:50:00.522 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:50:00.522 +02:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.Single[TSource](IEnumerable`1 source, Func`2 predicate)
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTasks(List`1 tokens, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 164
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildTaskGroup(Document document, List`1 signatories, List`1 transactionProcesses) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 124
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionTasks(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 71
   at ProcessPoint.Rest.Esigno.Services.SessionConfigurationBuilder.BuildSessionConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Services/ISessionConfigurationBuilder.cs:line 34
   at ProcessPoint.Rest.Esigno.Esigno.EsignoService.SetConfiguration(Envelope envelope) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Esigno/EsignoService.cs:line 86
   at ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession(Envelope rq) in /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno/Controllers/EsignoController.cs:line 37
   at lambda_method5(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-02 08:50:00.523 +02:00 [DBG] Connection id "0HNDP6DH456IG" completed keep alive response.
2025-07-02 08:50:00.523 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 500 null text/plain; charset=utf-8 9217.8684ms
2025-07-02 08:50:57.226 +02:00 [DBG] Connection id "0HNDP6DH456IH" received FIN.
2025-07-02 08:50:57.228 +02:00 [DBG] Connection id "0HNDP6DH456IH" accepted.
2025-07-02 08:50:57.229 +02:00 [DBG] Connection id "0HNDP6DH456IH" started.
2025-07-02 08:50:57.230 +02:00 [DBG] Connection id "0HNDP6DH456II" accepted.
2025-07-02 08:50:57.231 +02:00 [DBG] Connection id "0HNDP6DH456II" started.
2025-07-02 08:50:57.232 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 08:50:57.233 +02:00 [DBG] Connection id "0HNDP6DH456IH" stopped.
2025-07-02 08:50:57.233 +02:00 [DBG] Connection id "0HNDP6DH456IH" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 08:50:57.258 +02:00 [DBG] Connection 0HNDP6DH456II established using the following protocol: "Tls12"
2025-07-02 08:50:57.286 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 97118
2025-07-02 08:50:57.287 +02:00 [VRB] All hosts are allowed.
2025-07-02 08:50:57.289 +02:00 [DBG] POST requests are not supported
2025-07-02 08:50:57.289 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 08:50:57.289 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 08:50:57.289 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:50:57.290 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 08:50:57.290 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 08:50:57.290 +02:00 [DBG] Authorization was successful.
2025-07-02 08:50:57.290 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:50:57.290 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 08:50:57.290 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 08:50:57.290 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 08:50:57.290 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 08:50:57.290 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 08:50:57.290 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 08:50:57.290 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:50:57.293 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 08:50:57.293 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:50:57.293 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 08:50:57.293 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 08:50:57.293 +02:00 [DBG] Connection id "0HNDP6DH456II", Request id "0HNDP6DH456II:00000001": started reading request body.
2025-07-02 08:50:57.294 +02:00 [DBG] Connection id "0HNDP6DH456II", Request id "0HNDP6DH456II:00000001": done reading request body.
2025-07-02 08:50:57.295 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 08:50:57.295 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:50:57.296 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:50:57.296 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 08:50:57.296 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 08:50:57.296 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:50:57.296 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:50:57.296 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:50:57.296 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:50:57.296 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 08:50:57.296 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 08:50:59.811 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 2513.0237ms.
2025-07-02 08:50:59.812 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:50:59.812 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 08:50:59.812 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:50:59.812 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 08:50:59.813 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 08:50:59.813 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 08:50:59.814 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 08:50:59.815 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 08:50:59.816 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 08:50:59.816 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 08:50:59.816 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 08:50:59.816 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 08:50:59.816 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 08:50:59.830 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 08:50:59.830 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 08:50:59.830 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 08:50:59.831 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 2540.6362ms
2025-07-02 08:50:59.831 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 08:50:59.832 +02:00 [DBG] Connection id "0HNDP6DH456II" completed keep alive response.
2025-07-02 08:50:59.833 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 200 null application/json; charset=utf-8 2547.8108ms
2025-07-02 09:02:38.906 +02:00 [DBG] Connection id "0HNDP6DH456IJ" received FIN.
2025-07-02 09:02:38.934 +02:00 [DBG] Connection id "0HNDP6DH456IJ" accepted.
2025-07-02 09:02:38.934 +02:00 [DBG] Connection id "0HNDP6DH456IJ" started.
2025-07-02 09:02:38.935 +02:00 [DBG] Connection id "0HNDP6DH456IK" accepted.
2025-07-02 09:02:38.935 +02:00 [DBG] Connection id "0HNDP6DH456IK" started.
2025-07-02 09:02:38.937 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 09:02:38.937 +02:00 [DBG] Connection id "0HNDP6DH456IJ" stopped.
2025-07-02 09:02:38.938 +02:00 [DBG] Connection id "0HNDP6DH456IJ" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 09:02:38.965 +02:00 [DBG] Connection 0HNDP6DH456IK established using the following protocol: "Tls12"
2025-07-02 09:02:38.992 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 97861
2025-07-02 09:02:38.994 +02:00 [VRB] All hosts are allowed.
2025-07-02 09:02:38.994 +02:00 [DBG] POST requests are not supported
2025-07-02 09:02:38.994 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 09:02:38.994 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 09:02:38.994 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 09:02:38.994 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 09:02:38.994 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 09:02:38.994 +02:00 [DBG] Authorization was successful.
2025-07-02 09:02:38.994 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 09:02:38.995 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 09:02:38.995 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 09:02:38.995 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 09:02:38.995 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 09:02:38.995 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 09:02:38.995 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 09:02:38.995 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 09:02:38.998 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 09:02:38.998 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 09:02:38.998 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 09:02:38.998 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 09:02:38.998 +02:00 [DBG] Connection id "0HNDP6DH456IK", Request id "0HNDP6DH456IK:00000001": started reading request body.
2025-07-02 09:02:38.998 +02:00 [DBG] Connection id "0HNDP6DH456IK", Request id "0HNDP6DH456IK:00000001": done reading request body.
2025-07-02 09:02:38.999 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 09:02:38.999 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 09:02:38.999 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 09:02:38.999 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 09:02:39.000 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 09:02:39.000 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:02:39.000 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:02:39.000 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:02:39.000 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:02:39.000 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 09:02:39.000 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 09:03:51.009 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 72009.1347ms.
2025-07-02 09:03:51.009 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 09:03:51.009 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 09:03:51.009 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 09:03:51.009 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 09:03:51.009 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 09:03:51.009 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 09:03:51.009 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 09:03:51.009 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 09:03:51.009 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:03:51.009 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:03:51.009 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 72015.0854ms
2025-07-02 09:03:51.009 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 09:03:51.010 +02:00 [DBG] Connection id "0HNDP6DH456IK" completed keep alive response.
2025-07-02 09:03:51.010 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 200 null application/json; charset=utf-8 72017.8755ms
2025-07-02 09:04:00.093 +02:00 [DBG] Connection id "0HNDP6DH456IL" received FIN.
2025-07-02 09:04:00.099 +02:00 [DBG] Connection id "0HNDP6DH456IL" accepted.
2025-07-02 09:04:00.100 +02:00 [DBG] Connection id "0HNDP6DH456IL" started.
2025-07-02 09:04:00.100 +02:00 [DBG] Connection id "0HNDP6DH456IM" accepted.
2025-07-02 09:04:00.100 +02:00 [DBG] Connection id "0HNDP6DH456IM" started.
2025-07-02 09:04:00.101 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 09:04:00.101 +02:00 [DBG] Connection id "0HNDP6DH456IL" stopped.
2025-07-02 09:04:00.128 +02:00 [DBG] Connection id "0HNDP6DH456IL" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 09:04:00.133 +02:00 [DBG] Connection 0HNDP6DH456IM established using the following protocol: "Tls12"
2025-07-02 09:04:00.135 +02:00 [INF] Request starting HTTP/1.1 GET https://localhost:44301/api/esigno/KillSession/T-b7d56eea-6865-476f-a9ea-626745 - null null
2025-07-02 09:04:00.135 +02:00 [VRB] All hosts are allowed.
2025-07-02 09:04:00.135 +02:00 [DBG] The request path  does not match the path filter
2025-07-02 09:04:00.135 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/KillSession/T-b7d56eea-6865-476f-a9ea-626745'
2025-07-02 09:04:00.135 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/KillSession/{id}' is valid for the request path '/api/esigno/KillSession/T-b7d56eea-6865-476f-a9ea-626745'
2025-07-02 09:04:00.135 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 09:04:00.136 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 09:04:00.136 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 09:04:00.136 +02:00 [DBG] Authorization was successful.
2025-07-02 09:04:00.136 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 09:04:00.138 +02:00 [INF] Route matched with {action = "KillSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] KillSession(System.String) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 09:04:00.138 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 09:04:00.138 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 09:04:00.138 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 09:04:00.138 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 09:04:00.138 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 09:04:00.138 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 09:04:00.141 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 09:04:00.141 +02:00 [DBG] Attempting to bind parameter 'id' of type 'System.String' ...
2025-07-02 09:04:00.141 +02:00 [DBG] Attempting to bind parameter 'id' of type 'System.String' using the name 'id' in request data ...
2025-07-02 09:04:00.141 +02:00 [DBG] Done attempting to bind parameter 'id' of type 'System.String'.
2025-07-02 09:04:00.141 +02:00 [DBG] Done attempting to bind parameter 'id' of type 'System.String'.
2025-07-02 09:04:00.141 +02:00 [DBG] Attempting to validate the bound parameter 'id' of type 'System.String' ...
2025-07-02 09:04:00.142 +02:00 [DBG] Done attempting to validate the bound parameter 'id' of type 'System.String'.
2025-07-02 09:04:00.142 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:04:00.142 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:04:00.142 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:04:00.142 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:04:00.142 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 09:04:00.142 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) with arguments (["T-b7d56eea-6865-476f-a9ea-626745"])
2025-07-02 09:04:00.553 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 410.9447ms.
2025-07-02 09:04:00.553 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:04:00.553 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 09:04:00.554 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:04:00.554 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 09:04:00.554 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:04:00.554 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:04:00.554 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 09:04:00.554 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 09:04:00.556 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 09:04:00.556 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 09:04:00.556 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 09:04:00.556 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 09:04:00.556 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 09:04:00.556 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 09:04:00.556 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:04:00.556 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 09:04:00.556 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) in 418.2096ms
2025-07-02 09:04:00.556 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 09:04:00.556 +02:00 [DBG] Connection id "0HNDP6DH456IM" completed keep alive response.
2025-07-02 09:04:00.556 +02:00 [INF] Request finished HTTP/1.1 GET https://localhost:44301/api/esigno/KillSession/T-b7d56eea-6865-476f-a9ea-626745 - 200 null application/json; charset=utf-8 421.1968ms
2025-07-02 10:15:55.814 +02:00 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2025-07-02 10:15:55.912 +02:00 [DBG] Hosting starting
2025-07-02 10:15:55.927 +02:00 [INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
2025-07-02 10:15:55.931 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-62613a68-acd0-4198-a2dc-fd288203331f.xml'.
2025-07-02 10:15:55.934 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-f557cdd1-6cf5-426e-918b-a1a451e50f7c.xml'.
2025-07-02 10:15:55.934 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-7c39ab3b-e83c-4526-97ba-341254a092a6.xml'.
2025-07-02 10:15:55.935 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-aa556005-fc1c-45cb-b2a8-5da5b47d6031.xml'.
2025-07-02 10:15:55.935 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-573b67e1-3f60-4edc-812d-6d10ba8f545e.xml'.
2025-07-02 10:15:55.936 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-bdab4f65-7d40-4823-9ab5-32f587b06b51.xml'.
2025-07-02 10:15:55.936 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-2ac45e63-6d4c-4195-af6c-7d189f29e805.xml'.
2025-07-02 10:15:55.936 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-e33865f6-1366-4409-aeff-4b5d9c08fd6b.xml'.
2025-07-02 10:15:55.937 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-110ed128-38a9-4672-b9f4-0db76829c6ba.xml'.
2025-07-02 10:15:55.937 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-1551515c-b6d2-48e5-9f3e-9b041ceb8348.xml'.
2025-07-02 10:15:55.938 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-ccc7f0bf-3b7f-4f36-94c1-a944e0c08ceb.xml'.
2025-07-02 10:15:55.939 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-01a1f70d-478b-4435-a13b-e020f77dd56d.xml'.
2025-07-02 10:15:55.939 +02:00 [DBG] Reading data from file '/Users/<USER>/.aspnet/DataProtection-Keys/key-04c3120c-d0c7-47cb-b9c8-d3b790a82cc6.xml'.
2025-07-02 10:15:55.942 +02:00 [DBG] Found key {62613a68-acd0-4198-a2dc-fd288203331f}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {f557cdd1-6cf5-426e-918b-a1a451e50f7c}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {7c39ab3b-e83c-4526-97ba-341254a092a6}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {aa556005-fc1c-45cb-b2a8-5da5b47d6031}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {573b67e1-3f60-4edc-812d-6d10ba8f545e}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {bdab4f65-7d40-4823-9ab5-32f587b06b51}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {2ac45e63-6d4c-4195-af6c-7d189f29e805}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {e33865f6-1366-4409-aeff-4b5d9c08fd6b}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {110ed128-38a9-4672-b9f4-0db76829c6ba}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {1551515c-b6d2-48e5-9f3e-9b041ceb8348}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {ccc7f0bf-3b7f-4f36-94c1-a944e0c08ceb}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {01a1f70d-478b-4435-a13b-e020f77dd56d}.
2025-07-02 10:15:55.944 +02:00 [DBG] Found key {04c3120c-d0c7-47cb-b9c8-d3b790a82cc6}.
2025-07-02 10:15:55.947 +02:00 [DBG] Considering key {2ac45e63-6d4c-4195-af6c-7d189f29e805} with expiration date 2025-08-12 09:00:30Z as default key.
2025-07-02 10:15:55.948 +02:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-02 10:15:55.955 +02:00 [DBG] Using managed symmetric algorithm 'System.Security.Cryptography.Aes'.
2025-07-02 10:15:55.955 +02:00 [DBG] Using managed keyed hash algorithm 'System.Security.Cryptography.HMACSHA256'.
2025-07-02 10:15:55.959 +02:00 [DBG] Using key {2ac45e63-6d4c-4195-af6c-7d189f29e805} as the default key.
2025-07-02 10:15:55.959 +02:00 [DBG] Key ring with default key {2ac45e63-6d4c-4195-af6c-7d189f29e805} was loaded during application startup.
2025-07-02 10:15:56.476 +02:00 [INF] Now listening on: https://localhost:44301
2025-07-02 10:15:56.476 +02:00 [INF] Now listening on: http://localhost:5063
2025-07-02 10:15:56.476 +02:00 [DBG] Loaded hosting startup assembly ProcessPoint.Rest.Esigno
2025-07-02 10:15:56.476 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-02 10:15:56.476 +02:00 [INF] Hosting environment: Development
2025-07-02 10:15:56.477 +02:00 [INF] Content root path: /Users/<USER>/RiderProjects/ProcessPoint.Rest.Esigno/ProcessPoint.Rest.Esigno
2025-07-02 10:15:56.477 +02:00 [DBG] Hosting started
2025-07-02 10:15:56.606 +02:00 [DBG] Connection id "0HNDP82LR2U7V" received FIN.
2025-07-02 10:15:56.609 +02:00 [DBG] Connection id "0HNDP82LR2U7V" accepted.
2025-07-02 10:15:56.610 +02:00 [DBG] Connection id "0HNDP82LR2U7V" started.
2025-07-02 10:15:56.617 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 10:15:56.640 +02:00 [DBG] Connection id "0HNDP82LR2U7V" stopped.
2025-07-02 10:15:56.643 +02:00 [DBG] Connection id "0HNDP82LR2U7V" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 10:15:56.766 +02:00 [DBG] Connection id "0HNDP82LR2U80" accepted.
2025-07-02 10:15:56.766 +02:00 [DBG] Connection id "0HNDP82LR2U80" started.
2025-07-02 10:15:56.802 +02:00 [DBG] Connection 0HNDP82LR2U80 established using the following protocol: "Tls12"
2025-07-02 10:15:56.819 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "SETTINGS" frame for stream ID 0 with length 24 and flags "NONE".
2025-07-02 10:15:56.821 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 10:15:56.821 +02:00 [VRB] Connection id "0HNDP82LR2U80" received "SETTINGS" frame for stream ID 0 with length 18 and flags "NONE".
2025-07-02 10:15:56.821 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 10:15:56.822 +02:00 [VRB] Connection id "0HNDP82LR2U80" received "WINDOW_UPDATE" frame for stream ID 0 with length 4 and flags 0x0.
2025-07-02 10:15:56.822 +02:00 [VRB] Connection id "0HNDP82LR2U80" received "HEADERS" frame for stream ID 1 with length 306 and flags "END_STREAM, END_HEADERS, PRIORITY".
2025-07-02 10:15:56.832 +02:00 [VRB] Connection id "0HNDP82LR2U80" received "SETTINGS" frame for stream ID 0 with length 0 and flags "ACK".
2025-07-02 10:15:56.850 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/index.html - null null
2025-07-02 10:15:56.851 +02:00 [DBG] Wildcard detected, all requests with hosts will be allowed.
2025-07-02 10:15:56.851 +02:00 [VRB] All hosts are allowed.
2025-07-02 10:15:56.948 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "HEADERS" frame for stream ID 1 with length 66 and flags "END_HEADERS".
2025-07-02 10:15:56.949 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "DATA" frame for stream ID 1 with length 4761 and flags "NONE".
2025-07-02 10:15:56.953 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "DATA" frame for stream ID 1 with length 0 and flags "END_STREAM".
2025-07-02 10:15:56.954 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/index.html - 200 null text/html;charset=utf-8 104.8583ms
2025-07-02 10:15:57.078 +02:00 [VRB] Connection id "0HNDP82LR2U80" received "HEADERS" frame for stream ID 3 with length 100 and flags "END_STREAM, END_HEADERS".
2025-07-02 10:15:57.079 +02:00 [INF] Request starting HTTP/2 GET https://localhost:44301/swagger/v1/swagger.json - null null
2025-07-02 10:15:57.080 +02:00 [VRB] All hosts are allowed.
2025-07-02 10:15:57.156 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "HEADERS" frame for stream ID 3 with length 65 and flags "END_HEADERS".
2025-07-02 10:15:57.156 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "DATA" frame for stream ID 3 with length 8719 and flags "NONE".
2025-07-02 10:15:57.185 +02:00 [VRB] Connection id "0HNDP82LR2U80" sending "DATA" frame for stream ID 3 with length 0 and flags "END_STREAM".
2025-07-02 10:15:57.185 +02:00 [INF] Request finished HTTP/2 GET https://localhost:44301/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 105.5306ms
2025-07-02 10:16:07.837 +02:00 [DBG] Connection id "0HNDP82LR2U81" received FIN.
2025-07-02 10:16:07.842 +02:00 [DBG] Connection id "0HNDP82LR2U81" accepted.
2025-07-02 10:16:07.843 +02:00 [DBG] Connection id "0HNDP82LR2U82" accepted.
2025-07-02 10:16:07.843 +02:00 [DBG] Connection id "0HNDP82LR2U81" started.
2025-07-02 10:16:07.844 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 10:16:07.845 +02:00 [DBG] Connection id "0HNDP82LR2U81" stopped.
2025-07-02 10:16:07.846 +02:00 [DBG] Connection id "0HNDP82LR2U82" started.
2025-07-02 10:16:07.847 +02:00 [DBG] Connection id "0HNDP82LR2U81" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 10:16:07.875 +02:00 [DBG] Connection 0HNDP82LR2U82 established using the following protocol: "Tls12"
2025-07-02 10:16:07.881 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 97861
2025-07-02 10:16:07.882 +02:00 [VRB] All hosts are allowed.
2025-07-02 10:16:07.883 +02:00 [DBG] POST requests are not supported
2025-07-02 10:16:07.903 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 10:16:07.904 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 10:16:07.905 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:16:07.905 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 10:16:07.910 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 10:16:07.914 +02:00 [DBG] Authorization was successful.
2025-07-02 10:16:07.914 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:16:07.931 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 10:16:07.932 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 10:16:07.932 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 10:16:07.932 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 10:16:07.932 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 10:16:07.932 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 10:16:07.933 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:16:08.026 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:16:08.034 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 10:16:08.035 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 10:16:08.035 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 10:16:08.051 +02:00 [DBG] Connection id "0HNDP82LR2U82", Request id "0HNDP82LR2U82:00000001": started reading request body.
2025-07-02 10:16:08.058 +02:00 [DBG] Connection id "0HNDP82LR2U82", Request id "0HNDP82LR2U82:00000001": done reading request body.
2025-07-02 10:16:08.062 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 10:16:08.063 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 10:16:08.063 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 10:16:08.063 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 10:16:08.078 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 10:16:08.080 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:16:08.080 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:16:08.080 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:16:08.080 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:16:08.085 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 10:16:08.085 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 10:17:46.004 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 97915.1525ms.
2025-07-02 10:17:46.007 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:17:46.007 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:17:46.008 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:17:46.008 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:17:46.008 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:46.009 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:46.009 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:17:46.010 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 10:17:46.011 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 10:17:46.011 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 10:17:46.011 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 10:17:46.011 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 10:17:46.011 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 10:17:46.025 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:17:46.025 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:46.025 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:46.028 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 98092.9328ms
2025-07-02 10:17:46.029 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:17:46.030 +02:00 [DBG] Connection id "0HNDP82LR2U82" completed keep alive response.
2025-07-02 10:17:46.030 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 200 null application/json; charset=utf-8 98148.5889ms
2025-07-02 10:17:57.390 +02:00 [DBG] Connection id "0HNDP82LR2U83" received FIN.
2025-07-02 10:17:57.391 +02:00 [DBG] Connection id "0HNDP82LR2U83" accepted.
2025-07-02 10:17:57.392 +02:00 [DBG] Connection id "0HNDP82LR2U84" accepted.
2025-07-02 10:17:57.393 +02:00 [DBG] Connection id "0HNDP82LR2U84" started.
2025-07-02 10:17:57.395 +02:00 [DBG] Connection id "0HNDP82LR2U83" started.
2025-07-02 10:17:57.396 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 10:17:57.397 +02:00 [DBG] Connection id "0HNDP82LR2U83" stopped.
2025-07-02 10:17:57.397 +02:00 [DBG] Connection id "0HNDP82LR2U83" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 10:17:57.420 +02:00 [DBG] Connection 0HNDP82LR2U84 established using the following protocol: "Tls12"
2025-07-02 10:17:57.425 +02:00 [INF] Request starting HTTP/1.1 GET https://localhost:44301/api/esigno/KillSession/T-02c4ee27-536a-40c6-b572-688920 - null null
2025-07-02 10:17:57.425 +02:00 [VRB] All hosts are allowed.
2025-07-02 10:17:57.425 +02:00 [DBG] The request path  does not match the path filter
2025-07-02 10:17:57.425 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/KillSession/T-02c4ee27-536a-40c6-b572-688920'
2025-07-02 10:17:57.425 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/KillSession/{id}' is valid for the request path '/api/esigno/KillSession/T-02c4ee27-536a-40c6-b572-688920'
2025-07-02 10:17:57.425 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:17:57.425 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 10:17:57.426 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 10:17:57.427 +02:00 [DBG] Authorization was successful.
2025-07-02 10:17:57.429 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:17:57.432 +02:00 [INF] Route matched with {action = "KillSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] KillSession(System.String) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 10:17:57.432 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 10:17:57.432 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 10:17:57.432 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 10:17:57.432 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 10:17:57.432 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 10:17:57.432 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:17:57.435 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:17:57.435 +02:00 [DBG] Attempting to bind parameter 'id' of type 'System.String' ...
2025-07-02 10:17:57.436 +02:00 [DBG] Attempting to bind parameter 'id' of type 'System.String' using the name 'id' in request data ...
2025-07-02 10:17:57.436 +02:00 [DBG] Done attempting to bind parameter 'id' of type 'System.String'.
2025-07-02 10:17:57.436 +02:00 [DBG] Done attempting to bind parameter 'id' of type 'System.String'.
2025-07-02 10:17:57.436 +02:00 [DBG] Attempting to validate the bound parameter 'id' of type 'System.String' ...
2025-07-02 10:17:57.436 +02:00 [DBG] Done attempting to validate the bound parameter 'id' of type 'System.String'.
2025-07-02 10:17:57.436 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:17:57.436 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:17:57.436 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:17:57.436 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:17:57.436 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 10:17:57.436 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) with arguments (["T-02c4ee27-536a-40c6-b572-688920"])
2025-07-02 10:17:57.580 +02:00 [VRB] Connection id "0HNDP82LR2U80" received "GOAWAY" frame for stream ID 0 with length 8 and flags 0x0.
2025-07-02 10:17:57.581 +02:00 [DBG] Connection id "0HNDP82LR2U80" received FIN.
2025-07-02 10:17:57.585 +02:00 [DBG] The connection queue processing loop for 0HNDP82LR2U80 completed.
2025-07-02 10:17:57.586 +02:00 [DBG] Connection id "0HNDP82LR2U80" is closed. The last processed stream ID was 3.
2025-07-02 10:17:57.586 +02:00 [DBG] Connection id "0HNDP82LR2U80" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 10:17:57.587 +02:00 [DBG] Connection id "0HNDP82LR2U80" stopped.
2025-07-02 10:17:57.772 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 335.2837ms.
2025-07-02 10:17:57.772 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:17:57.772 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:17:57.772 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:17:57.772 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:17:57.772 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:57.773 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:57.773 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:17:57.773 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 10:17:57.775 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 10:17:57.775 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 10:17:57.775 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 10:17:57.775 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 10:17:57.775 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 10:17:57.776 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:17:57.776 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:57.776 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:17:57.776 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) in 343.9586ms
2025-07-02 10:17:57.776 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:17:57.776 +02:00 [DBG] Connection id "0HNDP82LR2U84" completed keep alive response.
2025-07-02 10:17:57.777 +02:00 [INF] Request finished HTTP/1.1 GET https://localhost:44301/api/esigno/KillSession/T-02c4ee27-536a-40c6-b572-688920 - 200 null application/json; charset=utf-8 351.886ms
2025-07-02 10:18:30.432 +02:00 [INF] Request starting HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - application/json 97861
2025-07-02 10:18:30.434 +02:00 [VRB] All hosts are allowed.
2025-07-02 10:18:30.434 +02:00 [DBG] POST requests are not supported
2025-07-02 10:18:30.434 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/StartSession'
2025-07-02 10:18:30.434 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/StartSession' is valid for the request path '/api/esigno/StartSession'
2025-07-02 10:18:30.434 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:18:30.434 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 10:18:30.435 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 10:18:30.435 +02:00 [DBG] Authorization was successful.
2025-07-02 10:18:30.435 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:18:30.435 +02:00 [INF] Route matched with {action = "StartSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] StartSession(ProcessPoint.Rest.Esigno.Models.Envelope) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 10:18:30.435 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 10:18:30.435 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 10:18:30.435 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 10:18:30.435 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 10:18:30.435 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 10:18:30.435 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:18:30.438 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:18:30.438 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 10:18:30.438 +02:00 [DBG] Attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' using the name '' in request data ...
2025-07-02 10:18:30.439 +02:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-02 10:18:30.439 +02:00 [DBG] Connection id "0HNDP82LR2U84", Request id "0HNDP82LR2U84:00000002": started reading request body.
2025-07-02 10:18:30.439 +02:00 [DBG] Connection id "0HNDP82LR2U84", Request id "0HNDP82LR2U84:00000002": done reading request body.
2025-07-02 10:18:30.440 +02:00 [DBG] JSON input formatter succeeded, deserializing to type 'ProcessPoint.Rest.Esigno.Models.Envelope'
2025-07-02 10:18:30.440 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 10:18:30.440 +02:00 [DBG] Done attempting to bind parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 10:18:30.440 +02:00 [DBG] Attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope' ...
2025-07-02 10:18:30.440 +02:00 [DBG] Done attempting to validate the bound parameter 'rq' of type 'ProcessPoint.Rest.Esigno.Models.Envelope'.
2025-07-02 10:18:30.441 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:18:30.441 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:18:30.441 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:18:30.441 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:18:30.441 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 10:18:30.441 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) with arguments (["ProcessPoint.Rest.Esigno.Models.Envelope"])
2025-07-02 10:20:39.928 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 129486.368ms.
2025-07-02 10:20:39.928 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:20:39.928 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:20:39.928 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:20:39.928 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:20:39.928 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:20:39.928 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:20:39.928 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:20:39.928 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 10:20:39.928 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 10:20:39.929 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 10:20:39.929 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 10:20:39.929 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 10:20:39.929 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 10:20:39.929 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:20:39.929 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:20:39.929 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:20:39.929 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno) in 129493.1431ms
2025-07-02 10:20:39.929 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.StartSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:20:39.929 +02:00 [DBG] Connection id "0HNDP82LR2U84" completed keep alive response.
2025-07-02 10:20:39.929 +02:00 [INF] Request finished HTTP/1.1 POST https://localhost:44301/api/esigno/StartSession - 200 null application/json; charset=utf-8 129497.254ms
2025-07-02 10:21:04.310 +02:00 [DBG] Connection id "0HNDP82LR2U85" received FIN.
2025-07-02 10:21:04.336 +02:00 [DBG] Connection id "0HNDP82LR2U85" accepted.
2025-07-02 10:21:04.339 +02:00 [DBG] Connection id "0HNDP82LR2U86" accepted.
2025-07-02 10:21:04.342 +02:00 [DBG] Connection id "0HNDP82LR2U85" started.
2025-07-02 10:21:04.342 +02:00 [DBG] Connection id "0HNDP82LR2U86" started.
2025-07-02 10:21:04.375 +02:00 [DBG] Failed to authenticate HTTPS connection.
System.IO.IOException: Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveHandshakeFrameAsync[TIOAdapter](CancellationToken cancellationToken)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](Boolean receiveFirst, Byte[] reAuthenticationData, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2025-07-02 10:21:04.376 +02:00 [DBG] Connection id "0HNDP82LR2U85" stopped.
2025-07-02 10:21:04.376 +02:00 [DBG] Connection id "0HNDP82LR2U85" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-02 10:21:04.407 +02:00 [DBG] Connection 0HNDP82LR2U86 established using the following protocol: "Tls12"
2025-07-02 10:21:04.408 +02:00 [INF] Request starting HTTP/1.1 GET https://localhost:44301/api/esigno/KillSession/T-99931b79-9945-4a24-b3a1-962af6 - null null
2025-07-02 10:21:04.408 +02:00 [VRB] All hosts are allowed.
2025-07-02 10:21:04.408 +02:00 [DBG] The request path  does not match the path filter
2025-07-02 10:21:04.408 +02:00 [DBG] 1 candidate(s) found for the request path '/api/esigno/KillSession/T-99931b79-9945-4a24-b3a1-962af6'
2025-07-02 10:21:04.408 +02:00 [DBG] Endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)' with route pattern 'api/Esigno/KillSession/{id}' is valid for the request path '/api/esigno/KillSession/T-99931b79-9945-4a24-b3a1-962af6'
2025-07-02 10:21:04.408 +02:00 [DBG] Request matched endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:21:04.408 +02:00 [VRB] The endpoint does not specify the IRequestSizeLimitMetadata.
2025-07-02 10:21:04.409 +02:00 [DBG] AuthenticationScheme: BasicAuthentication was successfully authenticated.
2025-07-02 10:21:04.409 +02:00 [DBG] Authorization was successful.
2025-07-02 10:21:04.409 +02:00 [INF] Executing endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:21:04.409 +02:00 [INF] Route matched with {action = "KillSession", controller = "Esigno"}. Executing controller action with signature System.Threading.Tasks.Task`1[System.Object] KillSession(System.String) on controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno).
2025-07-02 10:21:04.409 +02:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-02 10:21:04.409 +02:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-02 10:21:04.409 +02:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)"]
2025-07-02 10:21:04.409 +02:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-02 10:21:04.409 +02:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)"]
2025-07-02 10:21:04.409 +02:00 [DBG] Executing controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:21:04.412 +02:00 [DBG] Executed controller factory for controller ProcessPoint.Rest.Esigno.Controllers.EsignoController (ProcessPoint.Rest.Esigno)
2025-07-02 10:21:04.412 +02:00 [DBG] Attempting to bind parameter 'id' of type 'System.String' ...
2025-07-02 10:21:04.412 +02:00 [DBG] Attempting to bind parameter 'id' of type 'System.String' using the name 'id' in request data ...
2025-07-02 10:21:04.412 +02:00 [DBG] Done attempting to bind parameter 'id' of type 'System.String'.
2025-07-02 10:21:04.412 +02:00 [DBG] Done attempting to bind parameter 'id' of type 'System.String'.
2025-07-02 10:21:04.412 +02:00 [DBG] Attempting to validate the bound parameter 'id' of type 'System.String' ...
2025-07-02 10:21:04.412 +02:00 [DBG] Done attempting to validate the bound parameter 'id' of type 'System.String'.
2025-07-02 10:21:04.412 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:21:04.412 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:21:04.412 +02:00 [VRB] Action Filter: Before executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:21:04.412 +02:00 [VRB] Action Filter: After executing OnActionExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:21:04.412 +02:00 [INF] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) - Validation state: "Valid"
2025-07-02 10:21:04.412 +02:00 [VRB] Executing action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) with arguments (["T-99931b79-9945-4a24-b3a1-962af6"])
2025-07-02 10:21:04.830 +02:00 [INF] Executed action method ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 417.9101ms.
2025-07-02 10:21:04.831 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:21:04.832 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter.
2025-07-02 10:21:04.832 +02:00 [VRB] Action Filter: Before executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:21:04.832 +02:00 [VRB] Action Filter: After executing OnActionExecuted on filter Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter.
2025-07-02 10:21:04.832 +02:00 [VRB] Result Filter: Before executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:21:04.832 +02:00 [VRB] Result Filter: After executing OnResultExecuting on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:21:04.832 +02:00 [VRB] Before executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:21:04.832 +02:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-02 10:21:04.832 +02:00 [DBG] No information found on request to perform content negotiation.
2025-07-02 10:21:04.832 +02:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-02 10:21:04.832 +02:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-02 10:21:04.832 +02:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-02 10:21:04.832 +02:00 [INF] Executing OkObjectResult, writing value of type 'ProcessPoint.Rest.Esigno.Models.EnvelopeState'.
2025-07-02 10:21:04.833 +02:00 [VRB] After executing action result Microsoft.AspNetCore.Mvc.OkObjectResult.
2025-07-02 10:21:04.833 +02:00 [VRB] Result Filter: Before executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:21:04.833 +02:00 [VRB] Result Filter: After executing OnResultExecuted on filter Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter.
2025-07-02 10:21:04.833 +02:00 [INF] Executed action ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno) in 424.0881ms
2025-07-02 10:21:04.833 +02:00 [INF] Executed endpoint 'ProcessPoint.Rest.Esigno.Controllers.EsignoController.KillSession (ProcessPoint.Rest.Esigno)'
2025-07-02 10:21:04.833 +02:00 [DBG] Connection id "0HNDP82LR2U86" completed keep alive response.
2025-07-02 10:21:04.833 +02:00 [INF] Request finished HTTP/1.1 GET https://localhost:44301/api/esigno/KillSession/T-99931b79-9945-4a24-b3a1-962af6 - 200 null application/json; charset=utf-8 425.0815ms
