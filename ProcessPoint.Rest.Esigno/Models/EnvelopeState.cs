
using ProcessPoint.Rest.Esigno.Services;

namespace ProcessPoint.Rest.Esigno.Models;

public class EnvelopeState
{
    public EnvelopeState(SessionResult sessionResult, SignatoryHelper signatoryHelper)
    {
        EnvelopeId = sessionResult.SessionState.TokenId;
        ResultCode = sessionResult.SessionState.ResultCode;
        ResultText = sessionResult.SessionState.ResultText;
        SessionStart = sessionResult.SessionState.SessionStart ?? DateTime.MinValue;
        SessionExpire = sessionResult.SessionState.SessionExpire ?? DateTime.MinValue;
        SessionProlong = sessionResult.SessionState.SessionProlong ?? DateTime.MinValue;
        SessionStop = sessionResult.SessionState.SessionStop ?? DateTime.MinValue;
        ActiveTaskGroup = sessionResult.SessionState.ActiveTaskGroup ?? 0;
        Documents = new();
        foreach (SessionResultTaskGroup sessionResultTaskGroup in sessionResult.SessionTasks ?? Enumerable.Empty<SessionResultTaskGroup>())
        {
            Document document = new()
            {
                DocumentId = int.Parse(sessionResultTaskGroup.Id),
                Name = sessionResultTaskGroup.Name,
                ResultText = sessionResultTaskGroup.ResultText,
                ResultCode = sessionResultTaskGroup.ResultCode,
                DocumentUrl = sessionResultTaskGroup.DocumentFlattenURL
            };
            List<Token> tokens = new();
            foreach (var t in sessionResultTaskGroup.Task)
            {
                Token s = new()
                {
                    //Id = t.Id,
                    SignatoryId = int.Parse(t.Param.Single(x => x.Name == TaskParamType.UserId).Value),
                    Enabled = int.Parse(t.Param.Single(x => x.Name == TaskParamType.Enabled).Value),
                    Completed = int.Parse(t.Param.Single(x => x.Name == TaskParamType.Completed).Value),
                    TokenId = t.Id,
                    Status = t.ResultText,
                    StatusCode = t.ResultCode
                };
                if (t.Param.Any(x => x.Name == TaskParamType.SignDateTime))
                {
                    s.SignedAt = DateTime.Parse(t.Param.Single(x => x.Name == TaskParamType.SignDateTime).Value);
                }
                tokens.Add(s);
                document.Tokens = tokens;
            }
            Documents.Add(document);
        }
        Signatories = new();
        foreach (SessionResultAttendee attendee in sessionResult.SessionAttendees ?? Enumerable.Empty<SessionResultAttendee>())
        {
            Signatory s = new()
            {
                SignatoryId = signatoryHelper.IdPairs[attendee.DestinationAddress],
                RemoteId = attendee.Id,
                Name = attendee.Name,
                DestinationAddress = attendee.DestinationAddress,
                DestinationType = nameof(DestinationType.Email),
                SignatureType = string.Empty,
            };
            Signatories.Add(s);
        }
    }

    public int ContextId { get; set; }
    public string EnvelopeId { get; set; }
    public int ResultCode { get; set; }
    public string ResultText { get; set; }
    public DateTime SessionStart { get; set; }
    public DateTime SessionExpire { get; set; }
    public DateTime SessionProlong { get; set; }
    public DateTime SessionStop { get; set; }
    public int ActiveTaskGroup { get; set; }
    public List<Document> Documents { get; set; }
    public List<Signatory> Signatories { get; set; }
}
