namespace ProcessPoint.Rest.Esigno.Models;

public class Token
{
    public int DocumentId { get; set; }
    public string TokenId { get; set; }
    public int SignatoryId { get; set; }
    public int Enabled { get; set; }
    public int Required { get; set; }
    public int Completed { get; set; }
    public string Status { get; set; }
    public int StatusCode { get; set; }
    public string SignpadHeaderText { get; set; }
    public DateTime? SignedAt { get; set; }
}
