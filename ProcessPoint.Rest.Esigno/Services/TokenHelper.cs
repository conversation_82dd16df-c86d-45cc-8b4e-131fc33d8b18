using System.Text.RegularExpressions;
using UglyToad.PdfPig;

namespace ProcessPoint.Rest.Esigno.Services;

public class TokenHelper
{
    private static readonly Regex TokenRegex = new Regex(@"sig,id=(?<id>[\w\-]+)", RegexOptions.Compiled);
    
    //public Dictionary<string, int> TokenKeys = new ();

    public List<string> ExtractSignatureIds(Stream pdfStream)
    {
        if (pdfStream == null || !pdfStream.CanRead)
            throw new ArgumentException("PDF stream je neplatný nebo nečitelný.", nameof(pdfStream));

        var result = new List<string>();

        using var document = PdfDocument.Open(pdfStream);

        foreach (var page in document.GetPages())
        {
            string text = page.Text;

            foreach (Match match in TokenRegex.Matches(text))
            {
                var id = match.Groups["id"].Value;
                if (!string.IsNullOrWhiteSpace(id))
                    result.Add(id);
            }
        }

        return result;
    }
    public List<string> ExtractSignatureIdsFromBase64(string base64Pdf)
    {
        if (string.IsNullOrWhiteSpace(base64Pdf))
            throw new ArgumentException("Base64 string je prázdný.", nameof(base64Pdf));

        byte[] pdfBytes;

        try
        {
            pdfBytes = Convert.FromBase64String(base64Pdf);
        }
        catch (FormatException ex)
        {
            throw new ArgumentException("Neplatný Base64 formát.", nameof(base64Pdf), ex);
        }

        using var stream = new MemoryStream(pdfBytes);
        return ExtractSignatureIds(stream);
    }
}