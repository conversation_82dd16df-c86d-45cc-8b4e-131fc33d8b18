using Microsoft.Extensions.DependencyInjection;
using ProcessPoint.Rest.Esigno.Esigno;

namespace ProcessPoint.Rest.Esigno.Services;

/// <summary>
/// Rozšíření pro registraci služeb do DI kontejneru
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registruje Esigno služby do DI kontejneru
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="apiUrl">URL Esigno API (volitelné, použije se výchozí pokud není zadáno)</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddEsignoServices(this IServiceCollection services, string? apiUrl = null)
    {
        // Registrace základních služeb
        services.AddSingleton<IXmlSerializationService, XmlSerializationService>();
        services.AddTransient<ISessionConfigurationBuilder, SessionConfigurationBuilder>();
        services.AddTransient<ISessionResponseProcessor, SessionResponseProcessor>();
        services.AddScoped<SignatoryHelper>();
        
        // Registrace API klienta
        if (!string.IsNullOrEmpty(apiUrl))
        {
            services.AddTransient<IEsignoApiClient>(provider => new EsignoApiClient(apiUrl));
        }
        else
        {
            services.AddTransient<IEsignoApiClient>(provider => 
                new EsignoApiClient("https://api.sandbox.esigno.io/key-48BB357E1246FCCAF7EBD6A82BAF6275A5E913642DF9BA2B8B0EA1932F2DABB5/XyzmoController.asmx"));
        }
        
        // Registrace hlavní služby
        services.AddTransient<EsignoService>();
        
        return services;
    }
}
